name: Daily E2E Runner

on:
  schedule:
    - cron: "0 0 * * *" # 19:00 EST daily
  workflow_dispatch:

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }}

jobs:
  run_daily_tasks:
    runs-on: veritone-self-hosted-32gb

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Cypress dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y jq
          sudo apt-get install -y libnss3-tools
          sudo apt-get install -y wget libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
          curl -sSL https://dl.filippo.io/mkcert/latest?for=linux/amd64 -o mkcert
          sudo mv mkcert /usr/local/bin/
          sudo chmod +x /usr/local/bin/mkcert

      - name: Create and Trust mkcert Certificate
        run: |
          mkcert -install
          mkcert local.veritone.com
          # Trust certificate for Chrome
          CAROOT=$(mkcert -CAROOT)
          mkdir -p $HOME/.pki/nssdb
          certutil -N --empty-password -d "sql:$HOME/.pki/nssdb"
          certutil -d "sql:$HOME/.pki/nssdb" -A -t "C,," -n "mkcert" -i "$CAROOT/rootCA.pem"

      - name: Configure Hosts
        run: |
          echo "127.0.0.1 local.veritone.com" | sudo tee -a /etc/hosts

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22.x'
          cache: 'npm'

      - name: Corepack Setup
        run: |
          npm install -g corepack@latest
          corepack enable
          corepack prepare yarn@4.6.0 --activate

      - name: Install Dependencies
        run: |
          yarn install --immutable
          yarn add wait-on
        working-directory: client

      - name: Start Vite Server
        run: |
          echo "Starting Vite server..."
          yarn start:dev > app_startup.log 2>&1 &
          echo $! > vite.pid
          sleep 5
        working-directory: client

      - name: Wait for App
        run: |
          yarn wait-on --timeout 30000 https://local.veritone.com:4200/
          curl -k https://local.veritone.com:4200
        working-directory: client

      - name: Create cypress.env.json
        run: |
          echo '{
            "username": "${{ secrets.CYPRESS_USER1_USERNAME }}",
            "password": "${{ secrets.CYPRESS_USER1_PASSWORD }}",
            "users": {
              "user1": {
                "username": "${{ secrets.CYPRESS_USER1_USERNAME }}",
                "password": "${{ secrets.CYPRESS_USER1_PASSWORD }}"
              },
              "user2": {
                "username": "${{ secrets.CYPRESS_USER2_USERNAME }}",
                "password": "${{ secrets.CYPRESS_USER2_PASSWORD }}"
              }
            }
          }' > client/cypress.env.json

      - name: Run Health Check
        id: health_check
        uses: cypress-io/github-action@v6
        with:
          working-directory: client
          command: yarn cy:runLocal --spec=cypress/e2e/health-check/health-check.feature
          browser: chrome
          config: chromeWebSecurity=false
        continue-on-error: true

      - name: Check Health Check Results
        id: health_check_status
        run: |
          if [ "${{ steps.health_check.outcome }}" = "success" ]; then
            echo "health_check_passed=true" >> $GITHUB_OUTPUT
            echo "Health check passed - proceeding with full test suite"
          else
            echo "health_check_passed=false" >> $GITHUB_OUTPUT
            echo "Health check failed - skipping main Cypress tests"
          fi

      - name: Run Main Cypress Tests
        id: cypress
        if: steps.health_check_status.outputs.health_check_passed == 'true'
        uses: cypress-io/github-action@v6
        with:
          working-directory: client
          command: yarn cy:runLocal --spec=cypress/e2e/features/**/*.feature
          browser: chrome
          config: chromeWebSecurity=false
        continue-on-error: true

      - name: Upload Cypress screenshots on failure
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: client/cypress/screenshots

      - name: Parse Cucumber JSON Report
        id: parse_report
        if: always()
        run: |
          chmod +x .github/scripts/parse-report.sh
          .github/scripts/parse-report.sh

      - name: Send Slack Notification
        uses: rtCamp/action-slack-notify@v2

        if: always()
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: Cypress E2E Bot
          SLACK_CHANNEL: team-dev-glc
          SLACK_MESSAGE: |
            ${{ steps.parse_report.outputs.SLACK_MESSAGE }}

            *Health Check Status:* ${{ steps.health_check.outcome == 'success' && '✅ PASSED' || '❌ FAILED' }}
            ${{ steps.health_check_status.outputs.health_check_passed == 'false' && '*Main tests were skipped due to health check failure*' || '' }}
          SLACK_COLOR: ${{ (steps.parse_report.outputs.SLACK_MESSAGE_STATUS == 'FAILURE' || steps.health_check.outcome == 'failure') && 'danger' || 'good' }}

      - name: Cleanup
        if: always()
        run: |
          if [ -f client/vite.pid ]; then
            kill $(cat client/vite.pid) || true
          fi
